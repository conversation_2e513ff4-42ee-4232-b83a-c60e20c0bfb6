import { RoomNotFoundError } from '@rie/domain/errors';
import type { RoomInputSchema } from '@rie/domain/schemas';
import { dbRoomToRoom } from '@rie/domain/serializers';
import type { ResourceViewType } from '@rie/domain/types';
import { RoomsRepositoryLive } from '@rie/repositories';
import * as Effect from 'effect/Effect';
import type * as Schema from 'effect/Schema';

type RoomInput = Schema.Schema.Type<typeof RoomInputSchema>;

export class RoomsServiceLive extends Effect.Service<RoomsServiceLive>()(
  'RoomsServiceLive',
  {
    dependencies: [RoomsRepositoryLive.Default],
    effect: Effect.gen(function* () {
      const getAllRooms = () =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          return yield* repo.findAllRooms();
        });

      // Internal method for repository access (used by updateRoom, deleteRoom)
      const findRoomByIdInternal = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          const room = yield* repo.findRoomById(id);
          if (!room) {
            return yield* Effect.fail(new RoomNotFoundError({ id }));
          }
          return room;
        });

      // Public method with serializer (used by API routes)
      const getRoomById = (params: { id: string; view: ResourceViewType }) =>
        Effect.gen(function* () {
          const room = yield* findRoomByIdInternal(params.id);
          return dbRoomToRoom(room, params.view);
        });

      const createRoom = (room: RoomInput) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          return yield* repo.createRoom({ room });
        });

      const updateRoom = ({ id, room }: { room: RoomInput; id: string }) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          // Check if room exists using internal method
          const existingRoom = yield* findRoomByIdInternal(id);

          if (!existingRoom) {
            return yield* Effect.fail(new RoomNotFoundError({ id }));
          }
          return yield* repo.updateRoom({
            roomId: id,
            room,
          });
        });

      const deleteRoom = (id: string) =>
        Effect.gen(function* () {
          const repo = yield* RoomsRepositoryLive;
          // Check if room exists using internal method
          const existingRoom = yield* findRoomByIdInternal(id);
          if (!existingRoom) {
            return yield* Effect.fail(new RoomNotFoundError({ id }));
          }
          const result = yield* repo.deleteRoom(id);
          return result.length > 0;
        });

      return {
        getAllRooms,
        getRoomById,
        createRoom,
        updateRoom,
        deleteRoom,
      } as const;
    }),
  },
) { }
