import EditionRoomPage from '@/app/[locale]/bottin/locaux/[id]/editer/edit-room-page';
import { getFormSections } from '@/app/[locale]/infrastructures/helpers/get-form-sections';
import { roomFormSections } from '@/constants/bottin/room';
import { getQueryClientOptions } from '@/constants/query-client';
import { getGenericByIdOptions } from '@/hooks/bottin/generic-list.options';
import { controlledListsOptions } from '@/hooks/controlled-list/useControlledListsData';
import type { ControlledListKey, PageDetailsParams } from '@/types/common';
import {
  HydrationBoundary,
  QueryClient,
  dehydrate,
} from '@tanstack/react-query';
import { notFound } from 'next/navigation';

type EditRoomPageParams = PageDetailsParams;
export default async function EditRoomPage(props: EditRoomPageParams) {
  const params = await props.params;

  const { id, locale } = params;

  const formSections = await getFormSections({
    resourceName: 'rooms',
    sections: roomFormSections,
  });

  const queryClient = new QueryClient(getQueryClientOptions(locale));

  const t0 = performance.now();

  const controlledLists: ControlledListKey[] = [
    'building',
    'organisation',
    'roomCategory',
  ];

  const room = await Promise.all([
    queryClient.fetchQuery(
      getGenericByIdOptions({
        controlledListKey: 'local',
        id,
        view: 'edit',
      }),
    ),
    ...controlledLists.map((controlledListKey) =>
      queryClient.prefetchQuery(
        controlledListsOptions(controlledListKey, locale),
      ),
    ),
  ]);

  const t1 = performance.now();

  console.log(
    `Call to fetch controlledLists "building", "organization", "roomCategory" and "room" took ${t1 - t0} milliseconds.`,
  );

  if (!room) {
    return notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <EditionRoomPage formSections={formSections} id={id} locale={locale} />
    </HydrationBoundary>
  );
}
