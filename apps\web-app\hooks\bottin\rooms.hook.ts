import { useToast } from '@/components/hooks/use-toast';
import { useRouter } from '@/lib/navigation';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import {
  createGeneric,
  deleteGeneric,
  updateGeneric,
} from '@/services/bottin/generic-list.service';
import type { RoomEdit } from '@rie/domain/types';
import { useMutation, useQueryClient } from '@tanstack/react-query';

export const useCreateRoom = () => {
  const router = useRouter();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<RoomEdit, Error, RoomFormSchema>({
    mutationFn: async (payload) =>
      (await createGeneric({
        controlledListKey: 'local',
        payload,
      })) as unknown as RoomEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'local', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Room created successfully',
        variant: 'success',
      });
      router.push('/bottin/locaux');
    },
  });
};

export const useUpdateRoom = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<RoomEdit, Error, { id: string; payload: RoomFormSchema }>({
    mutationFn: async ({ id, payload }) =>
      (await updateGeneric({
        controlledListKey: 'local',
        id,
        payload,
      })) as unknown as RoomEdit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'local', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Room updated successfully',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description:
          error instanceof Error ? error.message : 'Failed to update room',
        variant: 'destructive',
      });
    },
  });
};

export const useDeleteRoom = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<void, Error, string>({
    mutationFn: async (id: string) => {
      await deleteGeneric({ controlledListKey: 'local', id });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['bottin', 'local', { view: 'list' }],
      });
      toast({
        title: 'Success',
        description: 'Room deleted successfully',
        variant: 'success',
      });
    },
  });
};
