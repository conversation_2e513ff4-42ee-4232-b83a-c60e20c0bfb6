'use client';
import { RoomForm } from '@/app/[locale]/bottin/locaux/form/room-form';
import { LoadingResource } from '@/components/loading-resource/loading-resource';
import { useUpdateRoom } from '@/hooks/bottin/rooms.hook';
import type { RoomFormSchema } from '@/schemas/bottin/room-form-schema';
import type { RoomFormSectionKey } from '@/types/bottin/room';

type EditRoomPageParams = {
  formSections: Record<RoomFormSectionKey, string>;
  id: string;
  initialData: RoomFormSchema;
};
export default function EditionRoomPage({
  formSections,
  initialData,
  id,
}: EditRoomPageParams) {
  const { mutate, status } = useUpdateRoom();

  if (status === 'pending') {
    return <LoadingResource />;
  }

  const handleOnSubmit = async (data: RoomFormSchema) => {
    await mutate({ id, payload: data });
  };

  return (
    <RoomForm
      defaultValues={initialData}
      formSections={formSections}
      onSubmit={handleOnSubmit}
    />
  );
}
